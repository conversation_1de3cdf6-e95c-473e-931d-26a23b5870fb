<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trigonometric Function Plotter</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="range"] {
            width: 100%;
        }
        .range-value {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 2px;
        }
        #plot {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .equation-display {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 18px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #2196f3;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .function-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        .function-btn {
            padding: 8px 16px;
            border: 2px solid #2196f3;
            background-color: white;
            color: #2196f3;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .function-btn.active {
            background-color: #2196f3;
            color: white;
        }
        .function-btn:hover {
            background-color: #1976d2;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌊 Trigonometric Function Plotter</h1>

        <div class="equation-display" id="equation">
            y = sin(x°)
        </div>

        <div class="controls">
            <div class="control-group">
                <label>Function Type:</label>
                <div class="function-buttons">
                    <button class="function-btn active" data-func="sin">sin</button>
                    <button class="function-btn" data-func="cos">cos</button>
                    <button class="function-btn" data-func="tan">tan</button>
                    <button class="function-btn" data-func="csc">csc</button>
                    <button class="function-btn" data-func="sec">sec</button>
                    <button class="function-btn" data-func="cot">cot</button>
                </div>
            </div>

            <div class="control-group">
                <label for="amplitude">Amplitude (A): <span id="amplitudeValue">1</span></label>
                <input type="range" id="amplitude" min="0.1" max="5" step="0.1" value="1">
                <div class="range-value">Controls the height of the wave</div>
            </div>

            <div class="control-group">
                <label for="frequency">Frequency (B): <span id="frequencyValue">1</span></label>
                <input type="range" id="frequency" min="0.1" max="5" step="0.1" value="1">
                <div class="range-value">Controls the number of cycles (Period = 2π/B)</div>
            </div>

            <div class="control-group">
                <label for="phaseShift">Phase Shift (degrees):</label>
                <input type="number" id="phaseShift" min="-360" max="360" step="1" value="0" style="width: 100px;">
                <div class="range-value">Horizontal shift (left/right in degrees)</div>
            </div>

            <div class="control-group">
                <label for="verticalShift">Vertical Shift (D): <span id="verticalShiftValue">0</span></label>
                <input type="range" id="verticalShift" min="-5" max="5" step="0.1" value="0">
                <div class="range-value">Vertical shift (up/down)</div>
            </div>

            <div class="control-group">
                <label>Coordinate System:</label>
                <div style="color: #666; font-size: 14px;">
                    <div>Domain: -360° to 360° (30° increments)</div>
                    <div>Range: -1 to 1</div>
                    <div>X-axis centered at y = 0</div>
                </div>
            </div>

            <div class="control-group">
                <label for="showGrid">Display Options:</label>
                <div>
                    <label style="font-weight: normal;">
                        <input type="checkbox" id="showGrid" checked> Show Grid
                    </label>
                </div>
                <div>
                    <label style="font-weight: normal;">
                        <input type="checkbox" id="showAxes" checked> Show Axes
                    </label>
                </div>
            </div>
        </div>

        <div id="plot"></div>
    </div>

    <script>
        // Trigonometric function plotter
        class TrigPlotter {
            constructor() {
                this.currentFunction = 'sin';
                this.amplitude = 1;
                this.frequency = 1;
                this.phaseShift = 0;
                this.verticalShift = 0;
                this.xMin = -360;
                this.xMax = 360;
                this.showGrid = true;
                this.showAxes = true;

                this.initializeControls();
                this.plotFunction();
            }

            initializeControls() {
                // Function buttons
                document.querySelectorAll('.function-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.function-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentFunction = e.target.dataset.func;
                        this.updateEquation();
                        this.plotFunction();
                    });
                });

                // Range inputs
                document.getElementById('amplitude').addEventListener('input', (e) => {
                    this.amplitude = parseFloat(e.target.value);
                    document.getElementById('amplitudeValue').textContent = this.amplitude;
                    this.updateEquation();
                    this.plotFunction();
                });

                document.getElementById('frequency').addEventListener('input', (e) => {
                    this.frequency = parseFloat(e.target.value);
                    document.getElementById('frequencyValue').textContent = this.frequency;
                    this.updateEquation();
                    this.plotFunction();
                });

                document.getElementById('phaseShift').addEventListener('input', (e) => {
                    // Convert degrees to radians for internal calculation
                    const phaseInDegrees = parseFloat(e.target.value) || 0;
                    this.phaseShift = (phaseInDegrees * Math.PI) / 180;
                    this.updateEquation();
                    this.plotFunction();
                });

                document.getElementById('verticalShift').addEventListener('input', (e) => {
                    this.verticalShift = parseFloat(e.target.value);
                    document.getElementById('verticalShiftValue').textContent = this.verticalShift;
                    this.updateEquation();
                    this.plotFunction();
                });



                // Display options
                document.getElementById('showGrid').addEventListener('change', (e) => {
                    this.showGrid = e.target.checked;
                    this.plotFunction();
                });

                document.getElementById('showAxes').addEventListener('change', (e) => {
                    this.showAxes = e.target.checked;
                    this.plotFunction();
                });
            }

            updateEquation() {
                let equation = `y = `;

                // Amplitude
                if (this.amplitude !== 1) {
                    equation += `${this.amplitude}`;
                }

                // Function
                equation += `${this.currentFunction}(`;

                // Frequency
                if (this.frequency !== 1) {
                    equation += `${this.frequency}`;
                }
                equation += `x°`;

                // Phase shift (convert radians to degrees for display)
                if (this.phaseShift !== 0) {
                    const phaseInDegrees = (this.phaseShift * 180) / Math.PI;
                    const sign = phaseInDegrees > 0 ? ' + ' : ' - ';
                    equation += `${sign}${Math.abs(phaseInDegrees).toFixed(0)}°`;
                }

                equation += `)`;

                // Vertical shift
                if (this.verticalShift !== 0) {
                    const sign = this.verticalShift > 0 ? ' + ' : ' - ';
                    equation += `${sign}${Math.abs(this.verticalShift)}`;
                }

                document.getElementById('equation').textContent = equation;
            }

            calculateFunction(x) {
                // Convert degrees to radians for calculation
                const xRadians = (x * Math.PI) / 180;
                const argument = this.frequency * xRadians + this.phaseShift;
                let result;

                switch (this.currentFunction) {
                    case 'sin':
                        result = Math.sin(argument);
                        break;
                    case 'cos':
                        result = Math.cos(argument);
                        break;
                    case 'tan':
                        result = Math.tan(argument);
                        break;
                    case 'csc':
                        const sinValue = Math.sin(argument);
                        result = sinValue === 0 ? undefined : 1 / sinValue;
                        break;
                    case 'sec':
                        const cosValue = Math.cos(argument);
                        result = cosValue === 0 ? undefined : 1 / cosValue;
                        break;
                    case 'cot':
                        const tanValue = Math.tan(argument);
                        result = tanValue === 0 ? undefined : 1 / tanValue;
                        break;
                    default:
                        result = Math.sin(argument);
                }

                if (result !== undefined) {
                    result = this.amplitude * result + this.verticalShift;
                }

                return result;
            }

            generatePoints() {
                const points = [];
                const step = 1; // 1 degree increments for smooth curve

                for (let x = this.xMin; x <= this.xMax; x += step) {
                    const y = this.calculateFunction(x);
                    if (y !== undefined && !isNaN(y) && isFinite(y)) {
                        // Clamp y values to the range [-1, 1] for better visualization
                        const clampedY = Math.max(-1, Math.min(1, y));
                        points.push({ x: x, y: clampedY });
                    }
                }

                return points;
            }

            plotFunction() {
                const points = this.generatePoints();
                const xValues = points.map(p => p.x);
                const yValues = points.map(p => p.y);

                const trace = {
                    x: xValues,
                    y: yValues,
                    type: 'scatter',
                    mode: 'lines',
                    line: {
                        color: '#2196f3',
                        width: 3
                    },
                    name: `${this.currentFunction}(x)`
                };

                const layout = {
                    title: {
                        text: `${this.currentFunction.toUpperCase()} Function - Cartesian Coordinate System`,
                        font: { size: 20 }
                    },
                    xaxis: {
                        title: 'Angle (degrees)',
                        range: [-360, 360],
                        showgrid: this.showGrid,
                        zeroline: this.showAxes,
                        showline: this.showAxes,
                        dtick: 30, // 30-degree increments
                        tickmode: 'linear',
                        tick0: -360,
                        tickvals: [-360, -330, -300, -270, -240, -210, -180, -150, -120, -90, -60, -30, 0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 360],
                        ticktext: ['-360°', '-330°', '-300°', '-270°', '-240°', '-210°', '-180°', '-150°', '-120°', '-90°', '-60°', '-30°', '0°', '30°', '60°', '90°', '120°', '150°', '180°', '210°', '240°', '270°', '300°', '330°', '360°']
                    },
                    yaxis: {
                        title: 'Value',
                        range: [-1.2, 1.2], // Slightly larger than [-1,1] for better visibility
                        showgrid: this.showGrid,
                        zeroline: true, // Always show the x-axis (y=0 line)
                        showline: this.showAxes,
                        dtick: 0.5,
                        tickmode: 'linear',
                        tick0: -1,
                        zerolinecolor: '#000000', // Make the x-axis (y=0) line black and prominent
                        zerolinewidth: 2
                    },
                    plot_bgcolor: 'white',
                    paper_bgcolor: 'white',
                    margin: { t: 60, r: 30, b: 60, l: 60 }
                };

                const config = {
                    responsive: true,
                    displayModeBar: true,
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
                    displaylogo: false
                };

                Plotly.newPlot('plot', [trace], layout, config);
            }
        }

        // Initialize the plotter when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new TrigPlotter();
        });
    </script>
</body>
</html>